const express = require('express')
const app = express();
const PORT = process.env.PORT || 8080;
const fs = require('fs');
var favicon = require('serve-favicon');
const axios = require('axios');
var cors = require('cors');
var bodyParser = require('body-parser')
require('dotenv').config();
var cookieParser = require('cookie-parser');
const multer = require('multer');
app.use(cookieParser());
const {Translate} = require('@google-cloud/translate').v2;
const translate = new Translate();
var ddrequest = require("request");
const { verify } = require('@adcaptcha/node');
const fetch = require("node-fetch");
  //npm install node-fetch@2
const textToSpeech = require('@google-cloud/text-to-speech');

app.use(cors());
app.use(bodyParser.json({ limit: '4mb' }));

const upload = multer();

const currentDemo = 'housewares';
const housewaresKey = process.env.SEARCH_KEY;
const megaKey = process.env.MEGA_KEY;

app.use(favicon(__dirname + '/ai-housewares-favicon.png'));

const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

app.get('/languages', async function(req, res) {
  const [languages] = await translate.getLanguages('en');

  // console.log('Languages:');
  res.json({
    results: languages
  });
});

app.post('/test-ai', async function(req, res) {
  const chat = await fetch("https://api.openai.com/v1/responses", {
      method: "POST",
      headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer " + process.env.OPENAI_KEY
      },
      body: JSON.stringify({
          "model": process.env.OPENAI_MODEL,
          "input": req.body.chat
      })
  });
  var responseJson = await chat.json();
  res.json(responseJson);
});

app.post('/ai-sayt', async function(req, res) {
  let convo = [
            {
                "role": "user",
                "content": req.body.question
            },
            {
                "role": "system",
                "content": "Using the text submitted by the user, please predict the question they are asking as it pertains to an online housewares store search. Respond with 5 likely questions that fit, with no other text and no bullets or numbering, and separate each question with a ~"
            }
        ];
  const chat = await fetch("https://api.openai.com/v1/responses", {
      method: "POST",
      headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer " + process.env.OPENAI_KEY
      },
      body: JSON.stringify({
          "model": process.env.OPENAI_MODEL,
          "input": convo
      })
  });
  var responseJson = await chat.json();
  let results = null;
  if(responseJson.output && responseJson.output.length > 0 && responseJson.output[0].content && responseJson.output[0].content.length > 0 && responseJson.output[0].content[0].text) {
    let r = responseJson.output[0].content[0].text.replace(/ ~ /g,'~');
    results = r.split('~');
  }
  let answer = {"results": results};
  res.json(answer);
});

app.get('/ai-voice', async function(req, res) {
  const client = new textToSpeech.TextToSpeechClient();
  let txt = "Welcome to Resolve's Housewares Demo";
  if(req.cookies.aiVoice) {
    txt = decodeURIComponent(req.cookies.aiVoice);
  }
  const request = {
    input: {text: txt},
    // Select the language and SSML voice gender (optional)
    voice: {languageCode: (req.cookies.langCode || 'en-US'), ssmlGender: 'MALE'},
    // select the type of audio encoding
    audioConfig: {audioEncoding: 'MP3'},
  };
  console.log('voice request', request);
  const [response] = await client.synthesizeSpeech(request);
  res.writeHead(200, {
      "Content-Type": "audio/mp3",
      "Content-Disposition": "attachment; filename=voice.mp3",
      "Content-Length": response.audioContent.length,
      "Content-Transfer-Encoding": "binary"
  });
  res.end(response.audioContent);
});

app.post('/image-context', async function(req, res) {
  let convo = req.body.chat;
  console.log('convo', convo);
  if(req.body.num == 1 || req.body.num == 2) {
    convo.push({
      "role": "system",
      "content": `Please respond with just a ${req.body.num}, followed by a colon, followed by a comma-delimited list of search terms that are better for finding what the user is looking for based on their request and the image provided - if the original request asked for sale items or stuff on sale, then add \" on sale\" to each search term - then after all of that place a colon, followed by a friendly message about the types of things being suggested and why they are good matches, followed by a | character, followed by a similar but more succinct message`
    });
  }
  else {
    convo.push({
      "role": "system",
      "content": "Please respond with just a 0, followed by a colon, followed by an error message, followed by another colon, then followed by a 0"
    });
  }
  convo.push({
    "role": "system",
    "content": ""
  })
  const chat = await fetch("https://api.openai.com/v1/responses", {
      method: "POST",
      headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer " + process.env.OPENAI_KEY
      },
      body: JSON.stringify({
          "model": process.env.OPENAI_MODEL,
          "input": convo
      })
  });
  var responseJson = await chat.json();
  console.log('image response', responseJson);
  let results = null;
  if(responseJson && responseJson.output && responseJson.output.length > 0 && responseJson.output[0].content && responseJson.output[0].content.length > 0 && responseJson.output[0].content[0].text) {
    results = responseJson.output[0].content[0].text;
  }
  let answer = {"results": results};
  // answer.results.searches = searches;
  res.json(answer);
});

app.post('/ai-json', async function(req, res) {
  try {
    let convo = req.body.chat;
    convo.push({
        "role": "system",
        "content": "Please analyze the user's request. For any part that responds with search terms, include phrases like \"murphy beds\" if the user is looking to organize a bedroom or free up space in a bachelor apartment. If there is any mention of a city, province, or country, ignore that part when determining if the request is relevant to the products found in this site. First, determine if the request has anything to do with this online store by checking if the user's request is asking about any products that might be found in this list of main categories: [\"Party Supplies\",\"Garden Decor\",\"Tablecloths\",\"Area Rugs\",\"birthday\",\"baby shower\",\"Ornaments\",\"Dog Toys\",\"Costumes\",\"Patio Conversation Sets\",\"Throw Pillows\",\"Artificial Flowers & Plants\",\"Door Mats\",\"Garden Tools & Accessories\",\"Outdoor Deals\",\"Patio Chairs & Benches\",\"Bath & Body\",\"Collectibles\",\"Furniture Deals\",\"Accent & End Tables\",\"Collectable Toys\",\"Christmas Trees\",\"Albums & Scrapbooking\",\"TV Stands & Entertainment Centers\",\"Wall Clocks\",\"Arts & Crafts\",\"Ear & Eye Care\",\"Hair Accessories\",\"Dining Chairs & Benches\",\"Pool Cleaning Tools\",\"Body Lotions & Creams\",\"Office Chairs\",\"Top Rated Health & Beauty\",\"Newborn Essentials (Preemie - 9M)\",\"Bar Stools & Counter Stools\",\"Lips\",\"Figurines\",\"Baby Bedding\",\"Changing Pad Covers\",\"Shower Curtains\",\"Solid Quilt Sets\",\"Typography & Signs Wall Decor\",\"Toys & Floats\",\"Cards & Board Game Gifts\",\"Girls' Clothing (Newborn - 4T)\",\"Outdoor Sofas & Loveseats\",\"Indoor Christmas Decorations\",\"Fitness & Yoga Equipment\",\"Coffee Mugs\",\"Baby Deals\",\"Health & Beauty Deals\",\"NFL\",\"Dining Deals\",\"Body Wash\",\"Coffee Tables\",\"Interactive Toys\",\"storage & organization\",\"Outdoor Games\",\"Nursing Covers\",\"Grill Tools & Accessories\",\"Dog Clothing\",\"Black Friday Health & Beauty Deals\",\"Nightstands\",\"Outdoor Playsets & Toys\",\"Kitchen & Bathroom Curtains\",\"Sideboards & Buffets\",\"Pattern Quilt Sets\",\"Activity Toys\",\"Black Friday Kitchen Deals\",\"Mens Bikes\",\"Cocktail Glasses\",\"More Deals\",\"Body Wash & Soap\",\"Bricks & Building Sets\",\"Storage & Organization Deals\",\"Wearable Blankets\",\"Hair Styling Products\",\"Bar Tools & Accessories\",\"Auto Accessories\",\"Decorative Objects\",\"Table & Chair Sets\",\"Cyber Monday Kitchen Deals\",\"Candle Holders\",\"Pot Holders & Oven Mitts\",\"Nails\",\"Body Lotions & Moisturizers\",\"Quilts\",\"Boys' Shoes\",\"Coffee\",\"Oral Care\",\"Pretend Play\",\"Shampoo & Conditioner\",\"Napkins\",\"Sheet Sets\",\"Decorative & Landscape Lighting\",\"Support Pillows\",\"Boys' Sets\",\"Coffee & Coffee Makers\",\"Building Toys\",\"Girls' Sleepwear\",\"Bed Blankets\",\"Bowls\",\"halloween\",\"Grooming\",\"Black Friday Rug Deals\",\"Vitamins & Supplements\",\"Wax Warmers & Wax Melts\",\"Outdoor Storage\",\"Top Rated Dining\",\"Tech Accessories\",\"furniture deals\",\"Health & Wellness\",\"Black Friday Furniture Deals\",\"Decals & Wallpaper\",\"Pattern Duvet Covers\",\"Hair Color\",\"Crib Sheets\",\"Kitchen Must Haves\",\"Kitchen Aprons\",\"bath\",\"Top Rated Kitchen\",\"Specialty Cookware\",\"Water Flossers & Floss\",\"Stuffed Animals\",\"Swaddles & Wearable Blankets\",\"Board & Card Games\",\"Baby Clothing Gift Sets\",\"Dining Tables\",\"Kitchen Organization\",\"Kitchen Tools & Gadgets\",\"Water Filters & Dispensers\",\"Girls' Hats\",\"gender reveal & baby shower party supplies\",\"Bedding Deals\",\"Playsets\",\"Table Lamps\",\"Holiday Storage\",\"Specialized Kitchen Tools\",\"often forgotten items\",\"Christmas Storage\",\"Gender Neutral Accessories\",\"Vehicles\",\"Humidifiers & Purifiers\",\"Multivitamins\",\"Bath Window Curtains\",\"Smart Watches\",\"Planters & Plant Stands\",\"Grilling Gifts\",\"Wreaths\",\"Baskets & Bins\",\"Fitted Sheets\",\"Specialty Flatware\",\"Seating\",\"Clothing Storage\",\"Tea Kettles & Pots\",\"Incontinence\",\"Kids Puzzles & Games\",\"Flatware Sets\",\"Kids Sheets\",\"Valentine's Day Gifts\",\"Puzzles\",\"Toothpaste\",\"Vases & Planters\",\"Teeth Whitening\",\"Gifts Under $100\",\"Baby Front & Hip Carriers\",\"Sewing Machines\",\"Outdoor Christmas Decorations\",\"Toilet Seats & Accessories\",\"Vacuum Cleaner Parts\",\"Thanksgiving Dining\",\"Women's Apparel & Accessories\",\"Hammocks\",\"Patio Dining Sets\",\"Outdoor Cookware\",\"Decorative Bookends\",\"Canning\",\"Mickey & Minnie\",\"Spring Entertaining\",\"Light Filtering Curtains\",\"Bathroom Wall Mirrors\",\"Black Friday Storage & Laundry\",\"Canisters\",\"Easter clothing\",\"Kitchen Deals\",\"Rubs & Seasonings\",\"Serving Utensils\",\"Makeup Brushes & Tools\",\"Kitchen Islands & Carts\",\"Folding Tables & Chairs\",\"Cabinet Knobs & Pulls\",\"Pendant & Island Lighting\",\"Platters & Trays\",\"Coffee & Tea\",\"Coat Racks & Umbrella Stands\",\"Candles & Fragrance\",\"work\",\"Beach & Pool\",\"Food & Drink\",\"Christmas Decor\",\"NHL\",\"Drawer Organizers\",\"Cooking Utensils\",\"Special Occasion & Holiday\",\"Decorative Boxes\",\"Over The Door Mirrors\",\"Home Fragrance\",\"Men's Apparel & Accessories\",\"dental care\",\"Hardware Accessories\",\"Wall Decals & Murals\",\"Loungewear\",\"Bed Skirts\",\"Christmas Wreaths & Garland\",\"Gallery Wall Art Sets\",\"Pot Racks\",\"Travel Accessories\",\"Tabletop Games\",\"Hair Brushes\",\"Decorative Shelving\",\"Drinking Glasses\",\"Baby Dishes & Utensils\",\"Kitchen Storage Solutions\",\"Smart Lighting & Outlets\",\"Kids Costumes\",\"Card & Casino Games\",\"Fireplaces & Accessories\",\"Abstract Wall Art\",\"Girls' Dresses\",\"Bib & Burp\",\"Wall Accents\",\"Newborn Accessories\",\"Easter Entertaining\",\"Sun Care\",\"Bathroom Shelving\",\"Dinnerware Sets\",\"Patio Umbrella Bases\",\"Diaper Bags\",\"Home & Office Electronics\",\"Prenatal Health and Wellness\",\"Massage\",\"Easter\",\"Bath Rugs\",\"Washcloths\",\"Night Lights\",\"Sink Organization\",\"Trash Bags & Liners\",\"Bird Houses\",\"Specialty\",\"Sink Accessories\",\"Rug Deals\",\"Metal Wall Decor\",\"Hair Styling Tools\",\"Thanksgiving Entertaining\",\"Gourmet Gift Baskets\",\"Soap & Lotion Dispensers\",\"Sports\",\"Artificial Outdoor Plants & Flowers\",\"Halloween Outdoor Décor\",\"Furniture\",\"Cyber Monday Health & Beauty Deals\",\"Beauty & Skin Care\",\"Counter Organizers\",\"Dinnerware & Drinkware\",\"Laundry Care\",\"Kids Board & Card Games\",\"Chandeliers\",\"Face\",\"Girls' Shoes\",\"Travel Size Hair Care\",\"Aromatherapy\",\"Tool Storage & Work Benches\",\"Beds\",\"Luggage Sets\",\"Dish Racks & Drainers\",\"Makeup Organizers & Storage\",\"Digital Frames & Photo Displays\",\"Textured Hair Care\",\"Christmas Stockings & Tree Skirts\",\"Wine Tools & Accessories\",\"Accent Mirrors\",\"Hair Treatments\",\"Boys' Hats\",\"Travel Must Haves\",\"Throw Blankets\",\"Dog Beds\",\"Wastebaskets\",\"Thermometers & Timers\",\"Specialty Cleaners\",\"Paring Knives\",\"Electric Heaters\",\"Blenders\",\"More Black Friday Deals\",\"Towel Racks\",\"Dress Up & Pretend Play\",\"Speakers & Radios\",\"Gear & Travel\",\"Tools & Gadgets\",\"Cake & Dessert Servers\",\"Plates\"] - if the user is clearly looking for products, please check every main category from that list against the specific items the user is looking for - if the query has nothing to do with any of those main categories, and also has nothing to do with finding ways to improve a room, then set response_code to 0, set response_verbose to a nicely-worded reply that tells the GB-Housewares does not have what they're looking for, set response_short to \"n/a\", set search_terms to a single element set to \"n/a\", and set all boolean properties to false. If the user is asking for information about shipping or the store's return policy, then set response_code to 0, set response_verbose to a friendly message in your own words that we do have information about that and to please wait a moment and they will be redirected to it, and set redirect to \"shipping\" or \"returns\" depending on which one they're looking for, plus set all other string properties to \"n/a\" and all Boolean properties to false. If it is asking for sale items or discounts or promo codes, but are not specific about the types of products they're looking for, then set response_code to a 1, set search_terms to a single element set to \"on sale\", set response_verbose to a brief, friendly message telling them here are some of our top sale items, plus set all other string properties to \"n/a\" and all Boolean properties to false. If it is asking for a list of items that match certain criteria and hasn't already set response_code to a 1 and search_terms to anything, then set response_code to a 1. If the user is asking help to find a specific product or products that they list in the question without a colon, then set the response_code to a 2. If the user is providing an image for more context, then check the original question against the image submitted, and re-analyze the user's request. If the response is a 1 or a 2, and if it looks like they want to see items that are on sale, then add \"on sale\" to each of the search terms listed in the search_terms property, but do not show \"on sale\" as a lone search term. If the response_code was set to a 1 or a 2, then check if the user specifically asked for items that go well with or match a room or something they have or even just has phrases similar to \"my home\" or \"my room\" or \"my apartment\" in the request. If that is the case, then set the search_terms property to an array of relevant search terms that might help them find what they're looking for, then set the response_verbose property to a request to see if the user is willing to share a photo of the room or items being mentioned. If they were not asking for anything to go well with something else, and if the response_code was set to a 1, then set the search_terms property to an array of search terms that might help them find what they're looking for, set the response_verbose property to an answer that describes the various items that can help satisfy their request, but do not repeat the list of search terms in that part of the response, set the response_short property to a more succinct version what was added to response_verbose. If the response_code was set to a 2, and the user was not asking for anything to match something, then set the search_terms property to an array of search terms that might help them find what they're looking for, set the response_verbose property to an answer to the user that describes how the various items can help satisfy their request, but do not provide the list of search terms in that part of the response, then also set the response_short property to a more succinct version what was added to response_verbose. If you have asked the user to share a photo for context, set the photo_requested property to true. If at any point the user asked to see new arrivals, then set the new_arrivals property to true. For any of the \"string\" type properties that were not updated by this point, set them to \"n/a\", and for any boolean properties that were not updated by this point, set them to false."
    });
    const chat = await fetch("https://api.openai.com/v1/chat/completions", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "Authorization": "Bearer " + process.env.OPENAI_KEY
        },
        body: JSON.stringify({
            "model": process.env.OPENAI_MODEL,
            "response_format": {
                "type": "json_schema",
                "json_schema": {
                  "name": "query_response",
                  "description": "schema for results after analyzing user query",
                  "strict": true,
                  "schema": {
                    "type": "object",
                    "properties": {
                      "response_code": {
                        "type": "integer",
                        "description": "response code to indicate how to handle the reply"
                      },
                      "response_verbose": {
                        "type": "string",
                        "description": "verbose response to user based on request"
                      },
                      "response_short": {
                        "type": "string",
                        "description": "short response to user based on request"
                      },
                      "search_terms": {
                        "type": ["array"],
                        "items": {
                          "type": "string"
                        }
                      },
                      "redirect": {
                        "type": ["string","null"],
                        "description": "url to load after response is displayed"
                      },
                      "photo_requested": {
                        "type": "boolean",
                        "description": "true if assistant asks user to provide a photo for context"
                      },
                      "new_arrivals": {
                        "type": "boolean",
                        "description": "true if user requested to see new items"
                      },
                      "on_sale": {
                        "type": "boolean",
                        "description": "true if user requested to see sale items"
                      }
                    },
                    "additionalProperties": false,
                    "required": [
                        "response_code",
                        "response_verbose",
                        "response_short",
                        "search_terms",
                        "redirect",
                        "photo_requested",
                        "new_arrivals",
                        "on_sale"
                    ]
                  }
                },
            },
            "messages": convo
        })
    });
    var responseJson = await chat.json();
    res.json(responseJson);
  }catch(err) {
    res.json({
      "error": 'unknown error'
    });
  }
});

app.post('/ai', async function(req, res) {
  let convo = req.body.chat;
  let inputLanguage = req.body.inputLanguage || null;

  if(JSON.stringify(convo).indexOf('Narrowing Down') == -1) {
    convo.push({
        "role": "system",
        "content": "Please analyze the user's request. For any part that responds with search terms, include phrases like \"murphy beds\" if the user is looking to organize a bedroom or free up space in a bachelor apartment. If the user is asking for information about shipping or the store's return policy, respond with just a 0 followed by a colon, then followed by a friendly message in your own words that we do have information about that and to please wait a moment and they will be redirected to it, then follow that part by another colon and then followed by \"redirect\", then another colon, and then depending on which info they are asking for follow that colon by the word \"shipping\" or \"returns\". If it is asking for sale items or discounts or promo codes, but are not specific about the types of products they're looking for, then respond with just a 1 followed by a colon then followed by the search term \"on sale\" then followed by another colon, then followed by a brief, friendly message telling them that here are some of our top sale items. If it is asking for a list of items that match certain criteria and does not have a colon and if it hasn't already responded with a 1 and a colon and other stuff, then respond with just a 1. If the user is asking help to find a specific product or products that they list in the question without a colon, then respond with just a 2. If the user is clarifying by answering the assistant's question, then check the original question against the answer provided, or against the image if one is provided for context, and re-analyze the user's request. If the response is a 1 or a 2, and if it looks like they want to see items that are on sale, then add \"on sale\" to each of the search terms extracted for the response, but do not show \"on sale\" as a lone search term. If the response was just a 1 or just a 2, then check if the user specifically asked for items that go well with or match a room or something they have or even just has phrases similar to \"my home\" or \"my room\" or \"my apartment\" in the request. If that is the case, then place after the colon a comma-delimited list of relevant search terms (including context like \"for a specific room\" or in a specific color, etc) that might help them find what they're looking for, then another colon, and then a request to see if they are willing to share a photo of the room or items they are mentioning. If they were not asking for anything to go well with something else, and if the response was just a 1, then place a colon after the 1 and follow that with just a comma-delimited list of search terms (including context like \"for a specific room\" or in a specific color, etc) that might help them find what they're looking for. After that comma-delimited list, place another colon, and following that, provide an answer to the user that briefly says the returned items will help satisfy their request, but do not repeat the list of search terms in that part of the response. Please follow this with a | character, followed by a similar but much more succinct/brief message. If the response was just a 2, and the user was not asking for anything to match something, then place a colon after the 2, and follow that with just a comma-delimited list of search terms (including context like \"for a specific room\" or in a specific color, etc) that might help them find what they're looking for. After that comma-delimited list, place another colon, and following that, provide an answer to the user that briefly says the returned items will help satisfy their request, but do not provide the list of search terms in that part of the response. Then place a | character, followed by a similar but more succinct message. If you have asked the user to share a photo for context, then add another colon to the response, followed by the word: photo. If the entire response appears to have a number, followed by a colon, followed by a comma-delimited list, followed by a colon, followed by another comma-delimited list, then please remove the 2nd comma-delimited list and the 2nd colon from the response. If at any point the user asked to see new arrivals, add the text \"~newArrivals\" to the end of the response. After checking all of this, if it is unclear what the user is asking, respond with a 0, followed by a colon, followed by a message indicating that you are unsure, and that they should stick to questions about Housewares products or general questions about the Housewares store."
    });
  }
  const chat = await fetch("https://api.openai.com/v1/responses", {
      method: "POST",
      headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer " + process.env.OPENAI_KEY
      },
      body: JSON.stringify({
          "model": process.env.OPENAI_MODEL,
          "input": convo
      })
  });
  var responseJson = await chat.json();
  let results = null;
  if(responseJson && responseJson.output && responseJson.output.length > 0 && responseJson.output[0].content && responseJson.output[0].content.length > 0 && responseJson.output[0].content[0].text) {
    results = responseJson.output[0].content[0].text;
  }
  let answer = {"results": results};
  res.json(answer);
});

app.get('/robots.txt', function(req, res) {
  let txt = `User-agent: *
Disallow: /`;
  res.setHeader('Content-type', "text/plain");
  res.setHeader('Content-disposition', 'inline; filename=robots.txt');
  res.send(txt);
});

app.get('/dd-drop*', async (req, res) => {
  // console.log('product id', req.query.pid);
  // get product:
  let options = {
    headers: {
      'Authorization': 'client-key ' + housewaresKey,
      'Content-Type': 'application/json',
      'X-Groupby-Customer-Id': 'demos',
      'skip-cache': 'true'
    }
  };

  try {
    let pdp = await axios.get('https://search.demos.groupbycloud.com/api/search/product?collection=housewares&productId=' + req.query.pid, options);
  // console.log('pdp data', pdp.data);
    // res.json(pdp.data);
    if(pdp.data && pdp.data.variants && pdp.data.variants.length > 0) {
      let v = 0;
      if(req.query.vid && req.query.vid != 0) {
        for(let i = 0; i < pdp.data.variants.length; i ++) {
          if(pdp.data.variants[i].id == req.query.vid) {
            v = i;
          }
        }
      }
      let price = 0;
      if(pdp.data.variants[v].priceInfo && pdp.data.variants[v].priceInfo.price) {
        price = pdp.data.variants[v].priceInfo.price;
      }
      let variantDetails = '';
      if(pdp.data.variants[v].colorInfo && pdp.data.variants[v].colorInfo.colors && pdp.data.variants[v].colorInfo.colors.length > 0 && pdp.data.variants[v].colorInfo.colors[0] != 'No Color') {
        variantDetails += `<div style="color: #676767; font-weight: normal; font-size: 1rem;">Color: ${pdp.data.variants[v].colorInfo.colors[0]}</div>`;
      }
      if(pdp.data.variants[v].sizes && pdp.data.variants[v].sizes.length > 0) {
        variantDetails += `<div style="color: #676767; font-weight: normal; font-size: 1rem;">Size: ${pdp.data.variants[v].sizes[0]}</div>`;
      }
      res.send(`<div style="width: 100%;"><table style="width: 100%; border-bottom: 1px solid #B5B5B5; margin-top: 1rem;">
        <tbody>
          <tr valign="top">
            <td style="width: 25%;">
              <img src="${(pdp.data.variants[v].images && pdp.data.variants[v].images.length > 0 && pdp.data.variants[v].images[0].uri) ? pdp.data.variants[v].images[0].uri : 'https://storage.googleapis.com/groupby-demo-images/image-not-found.png'}" style="width: 100%; height: auto; border: 1px solid #ddd;" />
            </td>
            <td>
              <div style="font-size: 1.25rem; padding-left: 1rem;">
                <div style="font-weight: bold;">${pdp.data.title}${variantDetails}<div>
              </div>
            </td>
            <td style="text-align: right; font-weight: bold;">
              <div style="padding-left: 1rem; font-size: 1.5rem;">
                $${price.toFixed(2)}
              </div>
            </td>
          </tr>
          <tr><td>&nbsp;</td><td>&nbsp;</td></tr>
        </tbody>
      </table></div>`);
    }
    else {
      res.status(400).send({
         message: "invalid product"
      });
    }
  } catch(err) {
    res.json({
      error: err
    });
  }

});

app.get('/dd*', async (req, res) => {
  // console.log('session id', req.query.sid);
  let args = {
    "name":"similar_items_pdp",
    "productID":[req.query.pid],
    "pageSize":"6",
    "collection":"housewares",
    "fields":["*"]
  }
  let options = {
    headers: {
      'Authorization': 'client-key ' + housewaresKey,
      'Content-Type': 'application/json',
      'X-Groupby-Customer-Id': 'demos',
      'skip-cache': 'true'
    }
  };

  try {
    let recs = await axios.post('https://recsapi.demos.groupbycloud.com/api/recommendation', args, options);

    if(recs.data && recs.data.records && recs.data.records.length > 0) {
      let refs = [];
      for(let i = 0; i < recs.data.records.length; i ++) {
        refs.push({
          "navigationName": "id",
          "type": "Value",
          "value": recs.data.records[i].id,
          "or": true
        });
      }
      if(refs.length > 0) {
        console.log('refs', refs);
        let prodArgs = {
            "area": 'Production',
            "collection": 'housewares',
            "pageSize": 6,
            "skip": 0,
            "dynamicFacet": true,
            "variantRollupKeys": [
                "colorFamilies",
                "colors",
                "sizes"
            ],
            "responseMask": [
                "*"
            ],
            "refinements": refs,
            "debug": false
        };
        let searchOptions = {
          headers: {
            'Authorization': 'client-key ' + housewaresKey,
            'Content-Type': 'application/json',
            'X-Groupby-Customer-Id': 'demos',
            'skip-cache': 'true'
          }
        };

        try {
          let search = await axios.post('https://search.demos.groupbycloud.com/api/search', prodArgs, searchOptions);

          if(search.data && search.data.records && search.data.records.length > 0) {
            console.log('recs search', search.data);
            let tiles = '<tr valign="top">';
            for(let i = 0; i < search.data.records.length; i ++) {
              let check = i + 1;
              let formattedTitle = '';
          		let title = search.data.records[i].allMeta.title;
          		var lastChar = '';
          		for(let j = 0; j < title.length; j ++) {
          			let c = title.toLowerCase().charAt(j);
          			if((c >= "a" && c <= "z") || (c >= "0" && c <= "9")) {
          				formattedTitle += c;
          				lastChar = c;
          			}
          			else {
          				if(c == ' ' || c == '-') {
          					if(lastChar != '-') {
          						formattedTitle += '-';
          						lastChar = '-';
          					}
          				}
          			}
          		}
              let price = '';
              let img = 'https://storage.googleapis.com/groupby-demo-images/image-not-found.png';
              if(search.data.records[i].allMeta.variants && search.data.records[i].allMeta.variants.length > 0) {
                if(search.data.records[i].allMeta.variants[0].images && search.data.records[i].allMeta.variants[0].images.length > 0 && search.data.records[i].allMeta.variants[0].images[0].uri) {
                  img = search.data.records[i].allMeta.variants[0].images[0].uri;
                }
                if(search.data.records[i].allMeta.variants[0].priceInfo && search.data.records[i].allMeta.variants[0].priceInfo.price) {
                  price = '$' + search.data.records[i].allMeta.variants[0].priceInfo.price.toFixed(2);
                  if(search.data.records[i].allMeta.variants[0].priceInfo.price != search.data.records[i].allMeta.variants[0].priceInfo.originalPrice) {
                    price = '<span style="color: #c03;">$' + search.data.records[i].allMeta.variants[0].priceInfo.price.toFixed(2) + '</span>';
                  }
                }
              }
              tiles += `<td style="width: 50%;">
                <a href="https://gb-apparel.groupby.cloud/product/${formattedTitle}/${search.data.records[i].allMeta.id}/" target="_blank" style="width: 100%; color: black; text-decoration: none;">
                  <img src="${img}" style="width: calc(100% - 2rem); height: 320px; object-fit: cover; border: 1px solid #ddd; margin: 1rem 1rem 0;" />
                  <div style="padding: .5rem 1.25rem 2rem;">
                    <div style="font-size: .9rem;">${search.data.records[i].allMeta.title}</div>
                    <div style="font-weight: bold; padding-top: .5rem; font-size: 1.125rem;">${price}</div>
                  </div>
                </a>
              </td>`;
              if((check%2) == 0) {
                tiles += '</tr>';
                if(i < (search.data.records.length - 1)) {
                  tiles += '<tr valign="top">';
                }
              }
            }
            let megamenu = '';
            if(req.query.mm && req.query.mm.trim() != '') {

              let megaOptions = {
                headers: {
                  'Authorization': 'client-key ' + megaKey,
                  'Content-Type': 'application/json',
                  'X-Groupby-Customer-Id': 'demos',
                  'skip-cache': 'true'
                }
              };

              let defaultMegamenu = req.query.mm;

              try {
                let megaData = await axios.get(`https://cm.demos.groupbycloud.com/api/megamenus/${defaultMegamenu}/categories`, megaOptions);

                if(megaData && megaData.data && megaData.data.items && megaData.data.items.length > 0) {
                  let orderNums = [];
                  for(let i = 0; i < megaData.data.items.length; i ++) {
                    orderNums.push({
                      ele: i,
                      onum: (megaData.data.items[i].metadata && megaData.data.items[i].metadata.order ? megaData.data.items[i].metadata.order : '1000')
                    });
                  }
                  orderNums.sort((a,b) => (parseInt(a.onum) > parseInt(b.onum)) ? 1 : (parseInt(b.onum > parseInt(a.onum)) ? -1 : 0));
                  let rows = [];
                  for(let i = 0; i < megaData.data.items.length; i ++) {
                    let url = '#';
                    let linkColor = 'black';
                    if(megaData.data.items[orderNums[i].ele].metadata && megaData.data.items[orderNums[i].ele].metadata.attribute && !megaData.data.items[orderNums[i].ele].metadata.facetCall) {
                      url = `/search//ref/${megaData.data.items[orderNums[i].ele].metadata.attribute}=${megaData.data.items[orderNums[i].ele].name}/`;
                    }
                    else if(megaData.data.items[orderNums[i].ele].metadata && megaData.data.items[orderNums[i].ele].metadata.type && megaData.data.items[orderNums[i].ele].metadata.type == 'lp') {
                      linkColor = megaData.data.items[orderNums[i].ele].metadata.colour;
                      url = megaData.data.items[orderNums[i].ele].urlAddress ? megaData.data.items[orderNums[i].ele].urlAddress : '#';
                    }
                    if(url != '#') {
                      url = url.replace(/\%/g, 'PERCENT');
                      url = url.replace(/\+/g, 'PLUS');
                      url = url.replace(/ /g, '+');
                      url = url.replace(/\./g, '~');
                      rows.push(`<td style="text-align: center;">
                        <div style="padding: .5rem 1rem;">
                        <a href="https://gb-apparel.groupby.cloud${url}" style="color: ${linkColor}; text-decoration: none; text-transform: uppercase;">${megaData.data.items[orderNums[i].ele].displayName}</a>
                        </div>
                      </td>`);
                    }
                  }
                  megamenu = `<table width="100%" border="0" cellspacing="0" cellpadding="0" style="${(rows.length > 4 ? '' : 'border-bottom: 1px solid #b5b5b5;')}font-size:14px;text-align:left;min-width:100%;table-layout:auto" role="presentation"><tbody><tr>`;
                  let newRow = 1000;
                  if(rows.length > 4) {
                    newRow = Math.ceil(rows.length / 2) - 1;
                  }
                  for(let i = 0; i < rows.length; i ++) {
                    megamenu += rows[i];
                    if(i == newRow) {
                      megamenu += '</tr></tbody></table><table width="100%" border="0" cellspacing="0" cellpadding="0" style="border-bottom: 1px solid #b5b5b5;font-size:14px;text-align:left;min-width:100%;table-layout:auto" role="presentation"><tbody><tr>';
                    }
                  }
                  megamenu += '</tr><tr><td>&nbsp;</td></tr></tbody></table>';
                }
              } catch(err) {
                res.json({
                  error: err
                });
              }
            }
            res.send(`<div style="width: 100%;"><table style="width: 100%;">
              <tbody>
                ${tiles}
              </tbody>
            </table></div>
            <table class="m_-7642274391802460241ee_element" style="font-size:14px;text-align:left;min-width:100%;table-layout:auto" width="100%" cellspacing="0" cellpadding="0" border="0" role="presentation">
            <tbody>
            <tr>
            <td style="font-family:Arial,sans-serif;border-width:0;padding:10px 20px;padding-top:25px;padding-right:20px;padding-bottom:25px;padding-left:20px">
            <center><img src="https://ci3.googleusercontent.com/meips/ADKq_NZv_lrBC7TRgS3kmCh1waswFg-uqiQVAe9nUBKQAOE2naRJXFV-SNZdFiP6ZjcMOauZ0MPlwbSvEnqqxxwUEuj-uJlvoNy2U617djjdSqnjoQQpHQca2JVti7xkvESVOB7ZSrA=s0-d-e1-ft#https://i.emlfiles4.com/cmpimg/3/9/9/9/5/3/files/3520_novastylinglogo450px.png" style="width:179px;min-height:auto;display:inline-block;max-width:100%;margin:0px;vertical-align:bottom" alt="" width="179" class="CToWUd" data-bit="iit"></center>
            </td>
            </tr>
            </tbody>
            </table>
            ${megamenu}
            `);
          }
          else {
            res.status(400).send({
               message: "no recommendations"
            });
          }
        } catch(err) {
          res.json({
            error: err
          });
        }

      }
    }
    else {
      res.status(400).send({
         message: "no recommendations"
      });
    }
  } catch(err) {
    res.json({
      error: err
    });
  }

});

const {Storage} = require('@google-cloud/storage');
var env = process.env.ENV;

const storage = new Storage('presales-demos',process.env.GOOGLE_STORAGE);
const bucketName = 'demos_content';


app.get('/assets/*', function(req, res) {

  const bucket = storage.bucket(bucketName);
  let urlPath = req.url.split('/');
  const file = bucket.file(currentDemo + '/' + env + req.url.split('?')[0]);

  file.exists(function(err,exists) {
    if(!exists) {
      res.send('error 404');
    }
    else {
      let parts = req.url.split('.');
      let ext = '';
      if(parts.length > 1) {
        ext = parts[1].split('?')[0];
      }
      // css, js
      // json
      if(ext == 'css' || ext == 'js') {
        let feed = file.createReadStream();
        var buf = '';
        feed.on('data', function(d) {
          buf += d;
        }).on('end', function() {
          if(ext == 'css') {
            res.type('css');
            // console.log('css file');
          }
          if(ext == 'js') {
            res.type('js');
            // console.log('js file');
          }
          res.send(buf.replace('.page *','\/* ' + env + ':' + req.hostname.toString() + ' *\/ .page *'));
        })
      }
      else {
        if(ext == 'json') {
          let feed = file.createReadStream();
          var buf = '';
          feed.on('data', function(d) {
            buf += d;
          }).on('end', function() {
            res.json(buf);
          })
        }
        else {
          const publicUrl = file.publicUrl();
          res.redirect(publicUrl);
        }
      }
    }
  });

});


app.get('/media/*', function(req, res) {
  // if(req.hostname.toString() == 'demo-s4r-apparel-dot-presales-demos-330114.uc.r.appspot.com') {
  //   env = 'dev';
  // }
  const bucket = storage.bucket(bucketName);
  let urlPath = req.url.split('/');
  // console.log('retrieving: groupbyinc-aua2LpqLwbWBL5AQ' + req.url);
  // res.send(req.url);

  const file = bucket.file(currentDemo + '/' + env + req.url);

  file.exists(function(err,exists) {
    if(!exists) {
      res.send('error 404');
    }
    else {
      let parts = req.url.split('.');
      let ext = '';
      if(parts.length > 1) {
        ext = parts[parts.length - 1];
      }
      // css, js
      // json
      if(ext == 'css' || ext == 'js') {
        let feed = file.createReadStream();
        var buf = '';
        feed.on('data', function(d) {
          buf += d;
        }).on('end', function() {
          res.send(buf);
        })
      }
      else {
        if(ext == 'json') {
          let feed = file.createReadStream();
          var buf = '';
          feed.on('data', function(d) {
            buf += d;
          }).on('end', function() {
            res.json(buf);
          })
        }
        else {
          if(ext == 'svg') {
            let filePath = req.url.split('/');
            file.getMetadata().then(function(data) {
              res.writeHead(200, {
                  "Content-Type": "image/svg+xml",
                  "Content-Disposition": "attachment; filename=" + filePath[filePath.length - 1],
                  "Content-Length": data[0].size,
                  "Content-Transfer-Encoding": "binary"
              });
              file.createReadStream({ encoding: null }).pipe(res);
            });
          }
          else {
            if(ext == 'pdf') {
              let filePath = req.url.split('/');
              file.getMetadata().then(function(data) {
                res.writeHead(200, {
                    "Content-Type": "application/pdf",
                    "Content-Disposition": "inline; filename=" + filePath[filePath.length - 1],
                    "Content-Length": data[0].size,
                    "Content-Transfer-Encoding": "binary"
                });
                file.createReadStream({ encoding: null }).pipe(res);
              });
            }
            else {
              // assume image
              ext = ext.replace('jpg','jpeg');
              let filePath = req.url.split('/');
              file.getMetadata().then(function(data) {
                res.writeHead(200, {
                    "Content-Type": "image/" + ext,
                    "Content-Disposition": "inline; filename=" + filePath[filePath.length - 1],
                    "Content-Length": data[0].size,
                    "Content-Transfer-Encoding": "binary"
                });
                file.createReadStream({ encoding: null }).pipe(res);
              });
            }
          }
        }
      }
    }
  });

});

app.use(function (req, res, next) {
  if(req.url.indexOf('/x/true/') != -1 && !req.cookies.appAuth) {
    // backdoor - allow access
    // res.cookie('appAuth', 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6Ilo0bmdvalJzYWtzRWlpd1N3Q3hiRCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Kdzf_VYBJUorrDyHDfvmK68ZxaH8hNHAtQBrF9hiAWiTO_nus7hoMNLOFesTHmI6KXj2O24GUHOMw1quqkaOIYV8Yeyn7NfIXoRC2MAhFDWDzR5RKU6a0GgYLtVwzHjUuk6reJfguCLiw1sqz6XQX9bEhWcGayR-YmeCUQ35vNoeuM27uHfuUFVe3NgNqiJ4wUwJ1xnsZzKlVFa91xF5BbgE9nKYxy5_HSlm5poIRuuiPxsi6lP-JG_8hw0Rt4mFNZSBz-iQihzvtHaoZkwBHb-bLtFo2veTMAJ2HhqT_xq7pNM0A0Vab7iq68_8xHDm9Xs-Jh-z39oPgthTJ9pbmA', { maxAge: 1000*60*60*24*30, httpOnly: true });
    // next();
    res.send('invalid URL');
  }
  else {
    if(req.query.code) {
      var options = {
        method: 'POST',
        url: 'https://groupbycloud.us.auth0.com/oauth/token',
        headers: {'content-type': 'application/x-www-form-urlencoded'},
        data: new URLSearchParams({
          grant_type: 'authorization_code',
          client_id: process.env.AUTH0_CLIENT,
          client_secret: process.env.AUTH0_SECRET,
          code: req.query.code,
          redirect_uri: `https://${req.get('host')}/callback`
        })
      };

      axios.request(options).then(function (response) {
        console.log('verifying',response.data);
        let decodedat = JSON.parse(Buffer.from(response.data.access_token.split('.')[1], 'base64').toString());
        console.log('decoded', decodedat);
        res.cookie('appAuth', response.data.access_token, { maxAge: 1000*60*60*24*30, httpOnly: true });
        if(req.cookies.currentPg && req.cookies.currentPg != '') {
          let gotoPg = req.cookies.currentPg;
          res.cookie('currentPg','', { maxAge: 1, httpOnly: true });
          res.redirect(gotoPg);
        }
        else {
          res.redirect('/');
        }
      }).catch(function (error) {
        // console.error(error);
        res.send('error - unable to load page');
      });
    }
    else {
      if(req.url == '/not-authorized') {
        if(req.cookies.appAuth) {
          // fully logout:
          let currentAuth = req.cookies.appAuth;

          let decodedsid = JSON.parse(Buffer.from(req.cookies.appAuth.split('.')[1], 'base64').toString());
          let sid = decodedsid.sub.replace('auth0|','');
          // res.json({
          //   'finding-sid': decodedsid
          // });

          res.cookie('appAuth', '', { maxAge: -1, httpOnly: true });
          let url = `https://groupbycloud.us.auth0.com/oidc/logout?logout_hint=${sid}&post_logout_redirect_uri=https://${req.get('host')}/not-authorized`;
          res.redirect(url);
        }
        else {
          res.send(`<!doctype html>
            <html>
            <head>
              <style type="text/css">
              html,body {
                height: 100%;
              }
              * {
                box-sizing: border-box;
              }
              body {
                margin: 0;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                text-align: center;
              }
              div {
                margin: .5rem 0;
                font-size: 1.25rem;
              }
              img {
                height: 3rem;
                width: auto;
              }
              </style>
            </head>
            <body>
              <div><img src="https://www.groupbyinc.com/media-library/groupby-horizontal-dark.png" /></div>
              <div>You are not authorized to view this site.</div>
              <div>If you require access to this demo,</div>
              <div>you may <a href="mailto:<EMAIL>">contact PreSales</a> to request it.</div>
              <div>&nbsp;</div>
              <div><a href="/">Click here to try again</a></div>
            </body>
            </html>`
          );
        }
      }
      else {
        if(!req.cookies.appAuth) {
          res.cookie('currentPg',decodeURIComponent(req.url), { maxAge: 1000*60*5, httpOnly: true });
          res.redirect(`https://groupbycloud.us.auth0.com/authorize?response_type=code&client_id=${process.env.AUTH0_CLIENT}&redirect_uri=https://${req.get('host')}/callback&scope=openid profile email ${process.env.AUTH0_PERMS}&audience=https://presales-demos-api&state=test`);
        }
        else {
          try {
            let decoded = JSON.parse(Buffer.from(req.cookies.appAuth.split('.')[1], 'base64').toString());
            console.log('checking', process.env.AUTH0_PERMS);
            if(decoded.email.indexOf('@groupbyinc.com') != -1 || decoded.permissions.indexOf(process.env.AUTH0_PERMS) != -1) {
              // login OK
              next();
            }
            else {
              // res.cookie('appAuth', '', { maxAge: -1, httpOnly: true });
              res.redirect('/not-authorized');
            }
          }
          catch (err) {
            // res.cookie('appAuth', '', { maxAge: -1, httpOnly: true });
            res.redirect('/not-authorized');
          }
        }
      }
    }
  }
});

async function testGoogleTextToSpeech(lang,audioBuffer) {
    const speech = require('@google-cloud/speech');
    const client = new speech.SpeechClient( { credentials: JSON.parse(process.env.SPEECH_KEY) });

    const audio = {
    content: audioBuffer.toString('base64'),
    };
    const config = {
    languageCode: lang,
    };
    const request = {
    audio: audio,
    config: config,
    };

    const [response] = await client.recognize(request);
    const transcription = response.results
    .map(result => result.alternatives[0].transcript)
    .join('\n');
    return transcription;
}

app.post('/translate', async function(req, res) {
  let [translations] = await translate.translate(decodeURIComponent(req.body.text), 'en-US');
  translations = Array.isArray(translations) ? translations : [translations];
  res.json({
    results: translations
  });
});

app.post('/body-tx', async (req, res) => {
  let txResults = [];
  console.log('lang', req.body.lang);
  for(let i = 0; i < req.body.translations.length; i ++) {
    let [translations] = await translate.translate(decodeURIComponent(req.body.translations[i]["txt-eng"]), req.body.lang);
    translations = Array.isArray(translations) ? translations : [translations];
    console.log('tx', translations);
    txResults.push({
      "ele": req.body.translations[i].ele,
      "txt-eng": req.body.translations[i]["txt-eng"],
      "txt-tx": translations
    });
  }
  res.json({
    results: txResults
  });
});

app.post('/speech', upload.any(), async (req, res) => {
    console.log("Getting text transcription..");
    let lang = (req.query.lang || 'en-US');
    let transcription = await testGoogleTextToSpeech(lang,req.files[0].buffer);
    console.log("Text transcription: " + transcription);
    res.json({
      transcription: transcription,
      lang: lang
    });
});

// app.get('/resubscribe', async (req, res) => {
//   let contactOptions = {
//     headers: {
//         'cache-control': 'no-cache',
//         'content-type': 'application/json',
//         'accept': 'application/json',
//         'authorization': "Basic " + Buffer.from("<EMAIL>:" + process.env.DOTDIGITALPW).toString("base64")
//     }
//   };
//
//   let contactRequest = {
//     unsubscribedContact: {
//       email: req.query.email
//     }
//   };
//
//   let resubscribe = await axios.post(`https://r2-api.dotdigital.com/v2/contacts/resubscribe-with-no-challenge`, contactRequest, contactOptions);
//
//   res.json({
//     "results": resubscribe.data
//   });
//
// });

app.post('/transactional-email', async (req, res) => {

  let userOptions = {
    headers: {
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'accept': 'application/json',
        'authorization': "Basic " + Buffer.from("<EMAIL>:" + process.env.DOTDIGITALPW).toString("base64")
    }
  }

  let userRequest = {
    identifiers: {email: req.body.uemail},
    dataFields: {SID: req.body.sid, PID: req.body.pid, MM: req.body.mm, VID: req.body.vid}
  }

  console.log('userRequest', userRequest);

  try {
    let updateUser = await axios.patch(`https://r2-api.dotdigital.com/contacts/v3/email/${encodeURIComponent(req.body.uemail)}?merge-option=overwrite`, userRequest, userOptions);

    if(updateUser.data.contactId) {
      await delay(500);

      let contactOptions = {
        headers: {
            'cache-control': 'no-cache',
            'content-type': 'application/json',
            'accept': 'application/json',
            'authorization': "Basic " + Buffer.from("<EMAIL>:" + process.env.DOTDIGITALPW).toString("base64")
        }
      };
      let contactRequest = {
        unsubscribedContact: {
          email: req.body.uemail
        }
      };
      let resubscribe = await axios.post(`https://r2-api.dotdigital.com/v2/contacts/resubscribe-with-no-challenge`, contactRequest, contactOptions);

      // await delay(500);

      axios({
        method: "post",
        url: `https://r2-api.dotdigital.com/v2/campaigns/${req.body.cid}/copy`,
        headers: userOptions.headers
      }).then(async function (response) {
        // rename copy:
        var d = new Date();
        const renameOptions = {
          method: 'put',
          url: `https://r2-api.dotdigital.com/v2/campaigns/${response.data.id}`,
          headers: userOptions.headers,
          data: {
            name: response.data.name.replace(' Template','').replace('Copy of DO NOT EDIT',(req.body.uemail.split('@')[0] + ' - ' + d.getTime()))
          }
        };
        let rnOptions = {
          headers: userOptions.headers
        };

        let rnData = {
          name: response.data.name.replace(' Template','').replace('Copy of DO NOT EDIT',(req.body.uemail.split('@')[0] + ' - ' + d.getTime())),
          subject: response.data.subject,
          fromName: response.data.fromName,
          htmlContent: response.data.htmlContent,
          plainTextContent: response.data.plainTextContent
        };

        await delay(500);

        let rn = await axios.put(`https://r2-api.dotdigital.com/v2/campaigns/${response.data.id}`, rnData, rnOptions);

          // send campaign
          let sendOptions = {
              headers:
              {
                  'cache-control': 'no-cache',
                  'content-type': 'application/json',
                  'accept': 'application/json',
                  'authorization': "Basic " + Buffer.from("<EMAIL>:" + process.env.DOTDIGITALPW).toString("base64")
              }
          };
          // // 2024-05-08T17:36:44.3884362Z
          // let now = new Date();
          // let sd = new Date(now.getTime() + 1000*20);
          // let dt = sd.toISOString();
          // ,
          // sendDate: dt
          let campaignRequest = {
            campaignId: response.data.id,
            contactIDs: [updateUser.data.contactId]
          };
          // console.log('payload', campaignRequest);
          let email = await axios.post('https://r2-api.dotdigital.com/v2/campaigns/send', campaignRequest, sendOptions);
          res.json(email.data);

  // res.json({
  //   final: rn.data
  // });

      }).catch(function (err) {
        res.json({
          "copy-error": err
        });
      });


    }
    else {
      res.json({
        "error": "failed - no contactId"
      });
    }
  } catch(err) {
    res.json({
      error: err
    });
  }

});

async function get404() {
  const bucket = storage.bucket(bucketName);
  const file = bucket.file(currentDemo + '/' + env + '/404.html');

  return new Promise((resolve, reject) => {
    let feed = file.createReadStream();
    var buf = '';
    feed.on('data', async function(d) {
      buf += d;
    }).on('end', async function() {
      let formattedPage = buf.replace(/\/dev\//g,'\/').replace(/\/live\//g,'\/').replace(/\/Global Assets\//g,'\/global-assets\/').replace(/\/Global%20Assets\//g,'\/global-assets\/');
      let completedPg = formattedPage;
      try {
        completedPg = await addHeaderFooter(formattedPage);
      } catch(error) {
        console.log('error getting header/footer');
      }
      resolve(completedPg);
    })
  });
}

app.post('/login', async function(req, res) {

  const apiKey = process.env.ADCAPTCHA_KEY;
  const token = req.body.token;

  var adcaptchaResponse = null;
  try {
    adcaptchaResponse = await verify(apiKey, token);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Failed to verify token' });
  }

  if(adcaptchaResponse && adcaptchaResponse.message && adcaptchaResponse.message == 'Token verified') {
    let userOptions = {
      headers: {
          'cache-control': 'no-cache',
          'content-type': 'application/json',
          'accept': 'application/json',
          'authorization': "Basic " + Buffer.from("<EMAIL>:" + process.env.DOTDIGITALPW).toString("base64")
      }
    };

    try {
      let contacts = await axios.get('https://r2-api.dotdigital.com/contacts/v3?data-fields=EMAIL', userOptions);

      let emailOK = false;
      if(contacts && contacts.data && contacts.data._items && contacts.data._items.length > 0) {
        for(let i = 0; i < contacts.data._items.length; i ++) {
          if(contacts.data._items[i].identifiers && contacts.data._items[i].identifiers.email && contacts.data._items[i].identifiers.email == req.body.email && req.body.pw.trim() != '') {
            emailOK = true;
          }
        }
      }
      if(emailOK) {
          res.json({
            "results": "success"
          });
      }
      else {
        res.json({
          "results": "failed"
        });
      }
    } catch(err) {
      res.json({
        error: err
      });
    }
  }
  else {
    res.json({
      "error": "You must verify that you are human"
    })
  }


});

app.post('/autocomplete*', async (req, res) => {
  let options = {
    headers: {
      'Authorization': 'client-key ' + housewaresKey,
      'Content-Type': 'application/json',
      'accept': 'application/json',
      'X-Groupby-Customer-Id': 'demos'
    }
  };

  if(req.cookies && req.cookies['gbi_visitorId']) {
    req.body.visitorId = req.cookies['gbi_visitorId'];
  }

  if(req.cookies && req.cookies['langCode'] && req.cookies['txUI']) {
    let [translations] = await translate.translate(decodeURIComponent(req.query.q), 'en-US');
    translations = Array.isArray(translations) ? translations : [translations];
    let auto = await axios.get(`https://autocomplete.demos.groupbycloud.com/api/request?collection=${req.query.collection}&area=${req.query.area}&searchItems=${req.query.pageSize}&query=${translations[0]}`, options);
    res.json(auto.data);
  }
  else {
    let auto = await axios.get(`https://autocomplete.demos.groupbycloud.com/api/request?collection=${req.query.collection}&area=${req.query.area}&searchItems=${req.query.pageSize}&query=${req.query.q.replace(/\#/g,'%23')}`, options);
    res.json(auto.data);
  }
});

app.post('/pdp-api', async function(req, res) {
  let options = {
    headers: {
      'Authorization': 'client-key ' + housewaresKey,
      'Content-Type': 'application/json',
      'X-Groupby-Customer-Id': 'demos',
      'skip-cache': 'true'
    }
  };

  console.log('pdp called', options, req.body);

  try {
    let pdp = await axios.get('https://search.demos.groupbycloud.com/api/search/product?collection=' + req.body.collection + '&productId=' + req.body.id, options);

    res.json(pdp.data);
  } catch(err) {
    res.json({
      error: err
    });
  }
});

app.post('/recs-api', async function(req, res) {
  let options = {
    headers: {
      'Authorization': 'client-key ' + housewaresKey,
      'Content-Type': 'application/json',
      'X-Groupby-Customer-Id': 'demos',
      'skip-cache': 'true'
    }
  };

  try {
    let recs = await axios.post('https://recsapi.demos.groupbycloud.com/api/recommendation', req.body, options);

    res.json(recs.data);
  } catch(err) {
    res.json({
      error: err
    });
  }
});

app.post('/facet-api', async function(req, res) {
  // let data = JSON.stringify(req.body);
  let options = {
    headers: {
      'Authorization': 'client-key ' + housewaresKey,
      'Content-Type': 'application/json',
      'X-Groupby-Customer-Id': 'demos',
      'skip-cache': 'true'
    }
  };
  console.log('facet payload', req.body);

  try {
    let facets = await axios.post('https://search.demos.groupbycloud.com/api/search/facet', req.body, options);
    res.json(facets.data);
  } catch(err) {
    res.json({
      error: err
    });
  }
});

app.post('/translate-block', async function(req, res) {
  let langCode = 'en-US';
  if(req.cookies && req.cookies['langCode']) {
    langCode = req.cookies['langCode'];
  }
  if(req.body.langCode) {
    langCode = req.body.langCode;
  }
  let [translations] = await translate.translate(req.body.block, langCode);
  translations = Array.isArray(translations) ? translations : [translations];
  res.json({
    results: translations
  });
});

app.post('/search-api', async function(req, res) {
  // let data = JSON.stringify(req.body);
  let options = {
    headers: {
      'Authorization': 'client-key ' + housewaresKey,
      'Content-Type': 'application/json',
      'X-Groupby-Customer-Id': 'demos',
      'skip-cache': 'true'
    }
  };

  if(req.cookies && req.cookies['gbi_visitorId']) {
    req.body.visitorId = req.cookies['gbi_visitorId'];
  }

  try {
    let search = await axios.post('https://search.demos.groupbycloud.com/api/search', req.body, options);
    res.json(search.data);
  } catch(err) {
    res.json({
      error: err
    });
  }
});

app.get('/test/', (req, res) => {
  res.redirect('/');
});

app.get('/dev/' + currentDemo + '/*', (req, res) => {
  res.redirect(req.url.replace('/dev',''));
});

app.get('/live/' + currentDemo + '/*', (req, res) => {
  res.redirect(req.url.replace('/live',''));
});

app.get('/test/', function(req, res) {
  res.json({
    "testing": req.hostname.toString()
  });
});

function urlDecode(url) {
    console.log(url);
	if(url) {
			if(url.indexOf('%') != -1) {
				url = decodeURIComponent(url);
			}
	    url = url.replace(/\+/g, ' ');
	    url = url.replace(/\*/g,'');
	    url = url.replace(/~/g,'.');
			// url = url.replace(/PLUSMINUS/g,'±');
			url = url.replace(/PLUS/g,'+');
			url = url.replace(/SLASH/g,'/');
			url = url.replace(/PERCENT/g,'%');
	}
    return url;
}

app.get('/docs-content/*', async (req, res) => {

    const bucket = storage.bucket(bucketName);
    let urlPath = req.url.split('/');
    const file = bucket.file(currentDemo + '/' + env + req.url + '.pdf');

    file.exists(function(err,exists) {
      if(!exists) {
        res.send('error 404');
      }
      else {
        let feed = file.createReadStream();
        var buf = '';
        feed.on('data', function(d) {
          buf += d;
        }).on('end', function() {
          let filePath = req.url.split('/');
          file.getMetadata().then(function(data) {
            res.writeHead(200, {
                "Content-Type": "application/pdf",
                "Content-Disposition": "inline; filename=" + filePath[filePath.length - 1],
                "Content-Length": data[0].size,
                "Content-Transfer-Encoding": "binary"
            });
            file.createReadStream({ encoding: null }).pipe(res);
          });
        })
      }
    });
});

app.get('/*', async (req, res) => {
  const bucket = storage.bucket(bucketName);
  let path = req.url.split('?')[0].split('/');
  let currentPg = path[1];
  let q = null;
  let prod = null;
  let doc = null;
  if(currentPg == '') {
    currentPg = 'homepage';
  }
  switch(currentPg) {
    case 'search':
    if(path.length > 2) {
      q = urlDecode(decodeURIComponent(path[2]));
    }
    break;
    case 'lp':
    if(path.length > 2) {
      q = urlDecode(decodeURIComponent(path[2]));
    }
    break;
    case 'product':
    if(path.length > 2) {
      prod = urlDecode(decodeURIComponent(path[2]));
      prod = prod.toLowerCase().replace(/\b[a-z]/g, function(letter) {
          return letter.toUpperCase();
      });
    }
    break;
    case 'docs':
    if(path.length > 2) {
      doc = path[2];
    }
    break;
  }
  const file = bucket.file(currentDemo + '/' + env + '/template.html');

  file.exists(function(err,exists) {
    if(!exists) {
      res.send('error - page not found');
    }
    else {
      let feed = file.createReadStream();
      var buf = '';
      feed.on('data', async function(d) {
        buf += d;
      }).on('end', async function() {
        let pageTitles = {
          "homepage": "Housewares | Home",
          "search": ("Housewares | Searching for " + (q || 'unknown')),
          "lp": ("Housewares | " + (q || 'unknown').replace(/\-/g,' ').toUpperCase()),
          "product": ("Housewares | " + (prod || 'unknown product')),
          "cart": "Housewares | Cart",
          "checkout": "Housewares | Checkout"
        }
        let d = new Date();
        let dTime = d.getTime();
        let cssList = `
        <link type="text/css" href="/assets/header.css?v=${dTime}" rel="stylesheet" media="screen"/>
        `;
        switch(currentPg) {
          case 'checkout':
          cssList += `
          <link type="text/css" href="/assets/checkout.css?v=${dTime}" rel="stylesheet" media="screen"/>`;
          break;
          case 'product':
          cssList += `
          <link type="text/css" href="/assets/pdp.css?v=${dTime}" rel="stylesheet" media="screen"/>`;
          break;
          case 'search':
          cssList += `
          <link type="text/css" href="/assets/search.css?v=${dTime}" rel="stylesheet" media="screen"/>`;
          break;
          case 'lp':
          cssList += `
          <link type="text/css" href="/assets/search.css?v=${dTime}" rel="stylesheet" media="screen"/>`;
          break;
        }
        let jsHeaderList = `
        <script src="/assets/settings.js?v=${dTime}"></script>
        <script src="/assets/apparel.js?v=${dTime}"></script>
        `;
        if(req.cookies && req.cookies['aiSearch']) {
          jsHeaderList += `<link type="text/css" href="/assets/ai-pdp.css?v=${dTime}" rel="stylesheet" />
          <script src="/assets/ai-pdp.js?v=${dTime}"></script>`;
        }
        let jsFooterList = '';
        switch(currentPg) {
          case 'checkout':
          jsFooterList += `
          <script src="/assets/checkout.js?v=${dTime}"></script>
          `;
          break;
          case 'product':
          jsFooterList += `
          <script src="/assets/pdp.js?v=${dTime}"></script>
          `;
          break;
          case 'search': case 'lp':
          jsFooterList += `
          <script src="/assets/search.js?v=${dTime}"></script>
          `;
          // get maxPrice:
          let showMaxPrice = '';
          // if(req.url.indexOf('/price=-') == -1) {
            let maxPriceBody = {
                "collection": "housewares",
                "area": "Production",
                "pageSize": 1,
                "fields": "*",
                "dynamicFacet": true,
                "variantRollupKeys": [
                    "colorFamilies",
                    "colors",
                    "sizes"
                ],
                "skip": 0,
                "sorts": [
                    {
                        "type": "Field",
                        "field": "price",
                        "order": "Descending"
                    }
                ]
            };
            let urlParameters = req.url.split('/');
            if(path[2].trim() != '') {
              maxPriceBody.query = urlDecode(decodeURIComponent(path[2].trim()));
            }
            if(path.length > 3) {
              let iStart = 3;
              if(req.url.indexOf('/lp/') != -1) {
                iStart = 4;
              }
              let refs = [];
              for(let i = iStart; i < urlParameters.length; i ++) {
                if(urlParameters[i] == 'ref' && i < (urlParameters.length - 1) && urlParameters[i + 1].trim() != '' && urlParameters[i + 1].indexOf('=') != -1) {
                  let pair = urlParameters[i+1].split('=');
                  if(pair.length == 2) {
                    refs.push({
                      "navigationName": urlDecode(pair[0]),
                      "type": "Value",
                      "displayName": "test",
                      "or": true,
                      "value": urlDecode(pair[1])
                    });
                  }
                }
                if(urlParameters[i] == 'range' && i < (urlParameters.length - 1) && urlParameters[i + 1].trim() != '' && urlParameters[i + 1].indexOf('=') != -1 && urlParameters[i + 1].indexOf(':') != -1) {
          				let parts = urlParameters[i+1].split('=');
                  if(parts[0] != 'price') {
                    let rangeParts = parts[1].split(':');
            				refs.push({
            					"navigationName": urlDecode(parts[0]),
            					"displayName": "test",
            					"type": "Range",
            					"or": true,
            					"low": rangeParts[0].replace('~','.'),
            					"high": rangeParts[1].replace('~','.')
            				});
                  }
          			}
              }
              if(refs.length > 0) {
                maxPriceBody.refinements = refs;
              }
            }
            let mpOptions = {
              headers: {
                'Authorization': 'client-key ' + housewaresKey,
                'Content-Type': 'application/json',
                'X-Groupby-Customer-Id': 'demos',
                'skip-cache': 'true'
              }
            };
            let mpSearch = await axios.post('https://search.demos.groupbycloud.com/api/search', maxPriceBody, mpOptions);
            if(mpSearch.data && mpSearch.data.records && mpSearch.data.records.length > 0 && mpSearch.data.records[0].allMeta && mpSearch.data.records[0].allMeta.variants && mpSearch.data.records[0].allMeta.variants.length > 0 && mpSearch.data.records[0].allMeta.variants[0].priceInfo && mpSearch.data.records[0].allMeta.variants[0].priceInfo.price) {
              showMaxPrice = `
              maximumPrice = ${mpSearch.data.records[0].allMeta.variants[0].priceInfo.price};
              `;
            }
          // }

          jsFooterList += `
          <script>
          ${showMaxPrice}
          </script>
          `;
          break;
        }
        let megaOptions = {
          headers: {
            'Authorization': 'client-key ' + megaKey,
            'Content-Type': 'application/json',
            'X-Groupby-Customer-Id': 'demos',
            'skip-cache': 'true'
          }
        };

        let defaultMegamenu = 'housewares-megamenu';
        if(req.cookies && req.cookies['megamenuOverride']) {
          defaultMegamenu = req.cookies['megamenuOverride'];
        }
        console.log('megamenu', defaultMegamenu);

        let megaData = await axios.get(`https://cm.demos.groupbycloud.com/api/megamenus/${defaultMegamenu}/categories`, megaOptions);

        let langOverride = '';
        if(req.cookies && req.cookies['langCode']) {
          langOverride = ` lang="${req.cookies['langCode'].split('-')[0]}"`;
        }

        let headSection = `<!doctype html>
        <html${langOverride}>
          <head>
            <meta charset="utf-8">
            <style type="text/css">
            .translating-body .tx-late {
              display: none!important;
            }
            </style>
            <meta name="viewport" content="width=device-width, initial-scale=1" />
            <meta name="robots" content="noindex">
            <link rel="preconnect" href="https://fonts.googleapis.com">
            <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
            <link href="https://fonts.googleapis.com/css2?family=Righteous&family=Montserrat:wght@300;400;600;700&family=Nunito+Sans:opsz,wght@6..12,300;6..12,400;6..12,600;6..12,700&family=Poppins:wght@200;300;400&display=swap" rel="stylesheet">
            <title>${pageTitles[currentPg] || 'Housewares'}</title>
            ${cssList}
        		${jsHeaderList}
            <script src="https://cdn.groupbycloud.com/gb-tracker-client-3.min.js"></script>
            <script>
            var currentPg = "${currentPg}";
            const gbTracker = new GbTracker('demos', 'Production');
            gbTracker.autoSetVisitor();
            </script>
            <!-- Google Tag Manager -->
            <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','GTM-MW9CDSV2');</script>
            <!-- End Google Tag Manager -->
          </head>
        	<body>
          <!-- Google Tag Manager (noscript) -->
          <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MW9CDSV2"
          height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
          <!-- End Google Tag Manager (noscript) -->
          <div class="megamenu-data invisible" data-menu="${defaultMegamenu}">${JSON.stringify(megaData.data)}</div>
          `;

        let regex = new RegExp('/' + currentDemo + '/','g');
        let formattedPage = buf.replace(/\/dev\//g,'\/').replace(/\/live\//g,'\/').replace(regex,'/');

        formattedPage += `<script src="https://widget.adcaptcha.com/index.js" defer></script>
        <div class="login-with-adcaptcha invisible">
          <form onsubmit="login(event);">
            <div class="login-left">
              <img src="/media/housewares-logo.png" class="login-logo" />
        			<h3>Start your journey</h3>
              <h2>Sign In</h2>
        			<div class="input-label">
                <img src="/media/icon-mail.png" />
        				<input type="email" class="login-email" placeholder="Email Address" required />
                <div>E-mail</div>
        			</div>
        			<div class="input-label">
                <img src="/media/icon-eye.png" onclick="togglePWType();" class="pw-toggle" />
        				<input type="password" class="login-pw" placeholder="Password" required />
                <div>Password</div>
        			</div>
              <div>
        				<button type="submit">Sign In</button>
        			</div>
              <div class="adcaptcha-holder">
                <div data-adcaptcha="${process.env.ADCAPTCHA_PLACEMENT}"></div>
              </div>
              <div class="login-alt">
                <div>or sign in with</div>
              </div>
              <div class="login-alt-btns">
                <div><img src="/media/logo-fb.svg" /></div>
                <div><img src="/media/logo-google.svg" /></div>
                <div><img src="/media/logo-apple.svg" /></div>
              </div>
              <div class="sign-up">
                Don't have an account? <a href="#" onclick="event.preventDefault();">Sign up</a>
              </div>
            </div>
            <div class="login-right">
              <div class="close-btn" onclick="cancelLogin();">&times</div>
              <div class="login-ribbon">test</div>
              <img src="/media/homepage-hero-summer-login.jpg" class="login-hero" />
            </div>
          </form>
        </div>`;
        let d2 = new Date();
        let footerSection = jsFooterList + `<footer>
        	<div>
            <!--DragDropZone-->

        <!--Menu_Column-->
        <div class="menu-column"><h3 class="tx-late">Customer Service</h3>
        <p><a href="../../../../../../" class="tx-late">Help &amp; FAQs</a></p>
        <p><a href="../../../../../../" class="tx-late">&nbsp;Order Tracking</a></p>
        <p><a href="../../../../../../" class="tx-late">Shipping &amp; Delivery</a></p>
        <p><a href="../../../../../../" class="tx-late">Returns</a></p>
        <p><a href="../../../../../../" class="tx-late">Contact Us</a></p></div>
        <!--Menu_Column-->
        <div class="menu-column"><h3 class="tx-late">Our Stores</h3>
        <p><a href="../../../../../../" class="tx-late">Find a Store</a></p>
        <p><a href="../../../../../../" class="tx-late">Visitor Services</a></p>
        <p><a href="../../../../../../" class="tx-late">Visitor Shopping Pass</a></p>
        <p><a href="../../../../../../" class="tx-late">Tell Us What You Think</a></p></div>
        <!--Menu_Column-->
        <div class="menu-column"><h3>Rezolve Ai Ltd</h3>
        <p><a href="https://rezolve.com" target="_blank" class="tx-late">Corporate Site</a></p>
        <p><a href="https://rezolve.com/about/" target="_blank" class="tx-late">About Rezolve</a></p>
        <p><a href="https://rezolve.com/" target="_blank" class="tx-late">Press and News</a></p>
        <p></p>
        <p></p></div>
            <div class="footer-right-column">
              <h3 class="tx-late">Be The First To Know With Our Emails</h3>
        <p><a href="/" class="btn tx-late">Sign Me Up</a></p>
        <hr />      <h3 class="tx-late">
                Connect With Us
              </h3>
              <div class="footer-social-media">
                <!--DragDropZone-->

        <!--Social_Media_Icon-->
        <a href="/"><img src="/media/social-media-icons/icon-linkedin.png" alt="LinkedIn" /></a>
        <!--Social_Media_Icon-->
        <a href="/"><img src="/media/social-media-icons/icon-facebook.png" alt="Facebook" /></a>
        <!--Social_Media_Icon-->
        <a href="/"><img src="/media/social-media-icons/icon-instagram2.png" alt="Instagram" /></a>
        <!--Social_Media_Icon-->
        <a href="/"><img src="/media/social-media-icons/icon-twitter.png" alt="Twitter" /></a>
        <!--Social_Media_Icon-->
        <a href="/"><img src="/media/social-media-icons/icon-pinterest.png" alt="Pinterest" /></a>
        <!--Social_Media_Icon-->
        <a href="/"><img src="/media/social-media-icons/icon-youtube2.png" alt="YouTube" /></a>
              </div>
            </div>
          </div>
        </footer>
        <div class="footer-bottom">
          <div>
            <div>
              <span class="tx-late">Copyright</span> © ${d2.getFullYear()} Rezolve Ai Ltd <span class="tx-late">All rights reserved.</span> <a href="https://groupbyinc.com/compliance/privacy-policy" target="_blank" class="tx-late">Privacy Policy</a> | <a href="https://groupbyinc.com/compliance/gdpr-compliance" target="_blank">GDPR</a> | <a href="https://groupbyinc.com/compliance/accessibility" target="_blank" class="tx-late">Accessibility</a>
            </div>
          </div>
          <img src="data:image/svg+xml;base64,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" class="settings-gear" onclick="showSettings();" />
        </div>
        </body>
        </html>`;
        if(doc) {
          formattedPage += `<div class="doc-holder">
            <iframe src="/docs-content/${doc}"></iframe>
          </div>`;
        }
        res.send(headSection + formattedPage + footerSection);
      })
    }
  });
});

app.listen(PORT, () => {
  console.log(`Example app listening on port ${PORT}!`)
});
