# Conversational Commerce Engine - Existing Prototype Technical Overview

## Executive Summary

This document provides a comprehensive analysis of the existing conversational commerce prototype built for GroupBy's housewares demonstration. The system implements a sophisticated AI-powered conversation flow that transforms traditional e-commerce search into a natural dialogue experience, serving as the foundation for developing a next-generation Conversational Commerce Engine.

## Architecture Overview

### Technology Stack
- **Backend**: Node.js/Express.js
- **AI Integration**: OpenAI API (GPT models)
- **Search Engine**: GroupBy Cloud Search API
- **Cloud Services**: Google Cloud Platform (Storage, Text-to-Speech, Speech-to-Text, Translation)
- **Authentication**: Auth0
- **Email Marketing**: DotDigital
- **File Storage**: Google Cloud Storage

### Core System Components

```javascript
// Core dependencies and setup
const express = require('express');
const axios = require('axios');
const { Translate } = require('@google-cloud/translate').v2;
const textToSpeech = require('@google-cloud/text-to-speech');
const fetch = require("node-fetch");

const app = express();
const currentDemo = 'housewares';
const housewaresKey = process.env.SEARCH_KEY;
```

## Conversation Flow Architecture

### 1. Core Conversation Endpoints

The system implements four primary conversation endpoints, each serving a specific purpose in the conversational commerce flow:

#### A. Search-as-You-Type (`/ai-sayt`) - Line 59
**Purpose**: Provides predictive search suggestions as users type
**Author Comment**: "responds with possible questions that they might be typing out"

```javascript
app.post('/ai-sayt', async function(req, res) {
  let convo = [
    {
      "role": "user",
      "content": req.body.question
    },
    {
      "role": "system",
      "content": "Using the text submitted by the user, please predict the question they are asking as it pertains to an online housewares store search. Respond with 5 likely questions that fit, with no other text and no bullets or numbering, and separate each question with a ~"
    }
  ];
  
  const chat = await fetch("https://api.openai.com/v1/responses", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": "Bearer " + process.env.OPENAI_KEY
    },
    body: JSON.stringify({
      "model": process.env.OPENAI_MODEL,
      "input": convo
    })
  });
  
  var responseJson = await chat.json();
  let results = null;
  if(responseJson.output && responseJson.output.length > 0 && 
     responseJson.output[0].content && responseJson.output[0].content.length > 0 && 
     responseJson.output[0].content[0].text) {
    let r = responseJson.output[0].content[0].text.replace(/ ~ /g,'~');
    results = r.split('~');
  }
  
  res.json({"results": results});
});
```

**Flow Process**:
1. User types partial query
2. System generates 5 predictive questions
3. Results split by `~` delimiter
4. Frontend displays suggestions

#### B. Image Context Analysis (`/image-context`) - Line 115
**Purpose**: Processes uploaded images to enhance conversation context
**Author Comment**: "handles an uploaded image in order to provide more context to the conversation"

```javascript
app.post('/image-context', async function(req, res) {
  let convo = req.body.chat;
  
  if(req.body.num == 1 || req.body.num == 2) {
    convo.push({
      "role": "system",
      "content": `Please respond with just a ${req.body.num}, followed by a colon, followed by a comma-delimited list of search terms that are better for finding what the user is looking for based on their request and the image provided - if the original request asked for sale items or stuff on sale, then add " on sale" to each search term - then after all of that place a colon, followed by a friendly message about the types of things being suggested and why they are good matches, followed by a | character, followed by a similar but more succinct message`
    });
  } else {
    convo.push({
      "role": "system",
      "content": "Please respond with just a 0, followed by a colon, followed by an error message, followed by another colon, then followed by a 0"
    });
  }
  
  // Process and return results...
});
```

**Flow Process**:
1. User uploads image with context
2. AI analyzes image + text request
3. Generates enhanced search terms
4. Returns formatted response with explanations

#### C. Structured JSON Response (`/ai-json`) - Line 156
**Purpose**: Advanced endpoint with structured JSON output using OpenAI's JSON Schema
**Author Comment**: "not used, but this was created for testing purposes when we were collaborating on that json-version using a schema"

```javascript
app.post('/ai-json', async function(req, res) {
  try {
    let convo = req.body.chat;
    convo.push({
      "role": "system",
      "content": "Please analyze the user's request. [EXTENSIVE SYSTEM PROMPT WITH 300+ PRODUCT CATEGORIES]"
    });
    
    const chat = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer " + process.env.OPENAI_KEY
      },
      body: JSON.stringify({
        "model": process.env.OPENAI_MODEL,
        "response_format": {
          "type": "json_schema",
          "json_schema": {
            "name": "query_response",
            "description": "schema for results after analyzing user query",
            "strict": true,
            "schema": {
              "type": "object",
              "properties": {
                "response_code": {"type": "integer"},
                "response_verbose": {"type": "string"},
                "response_short": {"type": "string"},
                "search_terms": {"type": ["array"], "items": {"type": "string"}},
                "redirect": {"type": ["string","null"]},
                "photo_requested": {"type": "boolean"},
                "new_arrivals": {"type": "boolean"},
                "on_sale": {"type": "boolean"}
              },
              "required": ["response_code", "response_verbose", "response_short", "search_terms", "redirect", "photo_requested", "new_arrivals", "on_sale"]
            }
          }
        },
        "messages": convo
      })
    });
    
    var responseJson = await chat.json();
    res.json(responseJson);
  } catch(err) {
    res.json({"error": 'unknown error'});
  }
});
```

**Key Features**:
- Uses OpenAI's structured output with JSON Schema
- Validates against 300+ product categories
- Returns standardized response format
- Handles error states gracefully

#### D. Main Conversation Handler (`/ai`) - Line 241
**Purpose**: Primary conversation endpoint with colon-delimited responses
**Author Comment**: "handles the user prompts by creating a large system prompt that informs ChatGPT how to format a response (did this before I knew about json schema support) - so it creates colons between parts that my front-end can split by in order to know how to show the answers etc"

```javascript
app.post('/ai', async function(req, res) {
  let convo = req.body.chat;
  let inputLanguage = req.body.inputLanguage || null;

  if(JSON.stringify(convo).indexOf('Narrowing Down') == -1) {
    convo.push({
      "role": "system",
      "content": `Please analyze the user's request. For any part that responds with search terms, include phrases like "murphy beds" if the user is looking to organize a bedroom or free up space in a bachelor apartment. 
      
      [RESPONSE FORMAT RULES]:
      - If asking for shipping/returns info: respond with "0:friendly message:redirect:shipping" or "returns"
      - If asking for sale items (non-specific): respond with "1:on sale:brief message about top sale items"
      - If asking for list of items matching criteria: respond with "1"
      - If asking for specific products: respond with "2"
      - If clarifying previous question: re-analyze against original + new context
      
      [SEARCH TERM RULES]:
      - If response is 1 or 2 and they want sale items: add "on sale" to each search term
      - If asking for room matching items: include search terms + request for photo
      - Format: number:search_terms:response_message|short_message
      - If requesting photo: add ":photo" to response
      - If requesting new arrivals: add "~newArrivals" to end
      
      If unclear what user is asking: respond with "0:message indicating uncertainty about Housewares products"`
    });
  }
  
  const chat = await fetch("https://api.openai.com/v1/responses", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": "Bearer " + process.env.OPENAI_KEY
    },
    body: JSON.stringify({
      "model": process.env.OPENAI_MODEL,
      "input": convo
    })
  });
  
  var responseJson = await chat.json();
  let results = null;
  if(responseJson && responseJson.output && responseJson.output.length > 0 && 
     responseJson.output[0].content && responseJson.output[0].content.length > 0 && 
     responseJson.output[0].content[0].text) {
    results = responseJson.output[0].content[0].text;
  }
  
  res.json({"results": results});
});
```

### 2. Response Format Analysis

The system uses a sophisticated response parsing mechanism based on colon-delimited strings:

#### Response Code System
- **0**: Error/Redirect (shipping info, returns, unrelated queries)
- **1**: General search (category-based queries)
- **2**: Specific product search (named products)

#### Response Format Structure
```
[CODE]:[SEARCH_TERMS]:[VERBOSE_MESSAGE]|[SHORT_MESSAGE]:[SPECIAL_FLAGS]
```

**Examples**:
```
1:coffee makers,espresso machines:Here are some great coffee options for your kitchen|Coffee gear for you
2:vitamix blender:This powerful blender will meet your needs|Perfect blender choice:photo
0:We have shipping information available:redirect:shipping
```

### 3. Search Integration

The conversation system seamlessly integrates with GroupBy's search infrastructure:

#### Product Data Retrieval
```javascript
// Product detail endpoint
app.get('/dd-drop*', async (req, res) => {
  let options = {
    headers: {
      'Authorization': 'client-key ' + housewaresKey,
      'Content-Type': 'application/json',
      'X-Groupby-Customer-Id': 'demos',
      'skip-cache': 'true'
    }
  };

  let pdp = await axios.get(
    'https://search.demos.groupbycloud.com/api/search/product?collection=housewares&productId=' + req.query.pid, 
    options
  );
  
  // Format and return product HTML
});
```

#### Recommendation Engine
```javascript
// Similar products endpoint
app.get('/dd*', async (req, res) => {
  let args = {
    "name": "similar_items_pdp",
    "productID": [req.query.pid],
    "pageSize": "6",
    "collection": "housewares",
    "fields": ["*"]
  };
  
  let recs = await axios.post(
    'https://recsapi.demos.groupbycloud.com/api/recommendation', 
    args, 
    options
  );
  
  // Process recommendations and return formatted HTML
});
```

### 4. Multi-Modal Capabilities

#### Voice Integration
```javascript
// Text-to-Speech
app.get('/ai-voice', async function(req, res) {
  const client = new textToSpeech.TextToSpeechClient();
  let txt = "Welcome to Resolve's Housewares Demo";
  if(req.cookies.aiVoice) {
    txt = decodeURIComponent(req.cookies.aiVoice);
  }
  
  const request = {
    input: {text: txt},
    voice: {languageCode: (req.cookies.langCode || 'en-US'), ssmlGender: 'MALE'},
    audioConfig: {audioEncoding: 'MP3'},
  };
  
  const [response] = await client.synthesizeSpeech(request);
  res.end(response.audioContent);
});

// Speech-to-Text
app.post('/speech', upload.any(), async (req, res) => {
  let lang = (req.query.lang || 'en-US');
  let transcription = await testGoogleTextToSpeech(lang, req.files[0].buffer);
  res.json({
    transcription: transcription,
    lang: lang
  });
});
```

#### Translation Support
```javascript
// Real-time translation
app.post('/translate', async function(req, res) {
  let [translations] = await translate.translate(decodeURIComponent(req.body.text), 'en-US');
  translations = Array.isArray(translations) ? translations : [translations];
  res.json({results: translations});
});

// Bulk translation for UI elements
app.post('/body-tx', async (req, res) => {
  let txResults = [];
  for(let i = 0; i < req.body.translations.length; i++) {
    let [translations] = await translate.translate(
      decodeURIComponent(req.body.translations[i]["txt-eng"]), 
      req.body.lang
    );
    txResults.push({
      "ele": req.body.translations[i].ele,
      "txt-eng": req.body.translations[i]["txt-eng"],
      "txt-tx": translations
    });
  }
  res.json({results: txResults});
});
```

## State Management & Session Handling

### Conversation Context Preservation
The system maintains conversation state through:

1. **Chat History**: Full conversation array passed with each request
2. **Cookie-based Preferences**: Language, voice settings, UI preferences
3. **Session Continuity**: Context checking for ongoing conversations

```javascript
// Context preservation example
if(JSON.stringify(convo).indexOf('Narrowing Down') == -1) {
  // Add system prompt only if not already in narrowing phase
}
```

### User Preference Management
```javascript
// Language preference handling
if(req.cookies && req.cookies['langCode'] && req.cookies['txUI']) {
  let [translations] = await translate.translate(decodeURIComponent(req.query.q), 'en-US');
  // Process translated query
}

// Voice preference handling
if(req.cookies.aiVoice) {
  txt = decodeURIComponent(req.cookies.aiVoice);
}
```

## Product Catalog Integration

### Category Management
The system leverages a comprehensive product taxonomy with 300+ categories:

```javascript
// Example categories from system prompt
const categories = [
  "Party Supplies", "Garden Decor", "Tablecloths", "Area Rugs",
  "Throw Pillows", "Artificial Flowers & Plants", "Door Mats",
  "Kitchen Organization", "Coffee Makers", "Storage & Organization",
  // ... 300+ more categories
];
```

### Search Term Enhancement
The AI automatically enhances search terms based on context:

```javascript
// Example from system prompt
"For any part that responds with search terms, include phrases like 'murphy beds' 
if the user is looking to organize a bedroom or free up space in a bachelor apartment"
```

### Sale Item Detection
```javascript
// Automatic sale term addition
"If the response is a 1 or a 2, and if it looks like they want to see items 
that are on sale, then add 'on sale' to each of the search terms extracted 
for the response, but do not show 'on sale' as a lone search term"
```

## Frontend Integration Points

### Response Parsing
The frontend expects colon-delimited responses for parsing:

```javascript
// Frontend parsing logic (conceptual)
const response = "1:coffee makers,espresso machines:Here are great options|Coffee gear:photo";
const [code, searchTerms, message, flags] = response.split(':');
const [verbose, short] = message.split('|');
```

### Dynamic Content Loading
```javascript
// Asset serving for dynamic content
app.get('/assets/*', function(req, res) {
  const bucket = storage.bucket(bucketName);
  const file = bucket.file(currentDemo + '/' + env + req.url.split('?')[0]);
  
  // Serve CSS, JS, and other assets dynamically
});
```

### Template System
```javascript
// Main template serving
app.get('/*', async (req, res) => {
  const file = bucket.file(currentDemo + '/' + env + '/template.html');
  
  // Dynamic page generation with conversation context
  let pageTitles = {
    "homepage": "Housewares | Home",
    "search": ("Housewares | Searching for " + (q || 'unknown')),
    "product": ("Housewares | " + (prod || 'unknown product'))
  };
});
```

## Security & Authentication

### Auth0 Integration
```javascript
app.use(function (req, res, next) {
  if(!req.cookies.appAuth) {
    res.redirect(`https://groupbycloud.us.auth0.com/authorize?response_type=code&client_id=${process.env.AUTH0_CLIENT}...`);
  } else {
    let decoded = JSON.parse(Buffer.from(req.cookies.appAuth.split('.')[1], 'base64').toString());
    if(decoded.email.indexOf('@groupbyinc.com') != -1 || decoded.permissions.indexOf(process.env.AUTH0_PERMS) != -1) {
      next(); // Authorized
    } else {
      res.redirect('/not-authorized');
    }
  }
});
```

## Performance Optimizations

### Caching Strategy
```javascript
headers: {
  'Authorization': 'client-key ' + housewaresKey,
  'Content-Type': 'application/json',
  'X-Groupby-Customer-Id': 'demos',
  'skip-cache': 'true' // Bypass cache for real-time data
}
```

### Async Processing
```javascript
// Non-blocking async operations
const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

// Parallel API calls where possible
let [translations] = await translate.translate(text, targetLang);
```

## Error Handling & Resilience

### API Error Management
```javascript
try {
  let pdp = await axios.get(apiUrl, options);
  // Process successful response
} catch(err) {
  res.json({error: err});
}
```

### Graceful Degradation
```javascript
let results = null;
if(responseJson && responseJson.output && responseJson.output.length > 0 && 
   responseJson.output[0].content && responseJson.output[0].content.length > 0 && 
   responseJson.output[0].content[0].text) {
  results = responseJson.output[0].content[0].text;
}
// Always return structured response even if AI fails
```

## Analytics & Monitoring

### Interaction Tracking
```javascript
// Google Analytics integration
<script>
var currentPg = "${currentPg}";
const gbTracker = new GbTracker('demos', 'Production');
gbTracker.autoSetVisitor();
</script>
```

### Conversation Logging
```javascript
console.log('convo', convo);
console.log('image response', responseJson);
console.log('voice request', request);
```

## Context-Aware Personalization Implementation

The system implements sophisticated personalization through multiple layers of user preference tracking and contextual adaptation:

### 1. **Cookie-Based Preference Management**

The system uses Express.js cookie parser to maintain persistent user preferences:

```javascript
var cookieParser = require('cookie-parser');
app.use(cookieParser());
```

### 2. **Language Personalization**

**Multi-Language Support with Persistent Preferences:**

```javascript
// Voice synthesis with language preference
app.get('/ai-voice', async function(req, res) {
  const client = new textToSpeech.TextToSpeechClient();
  let txt = "Welcome to Resolve's Housewares Demo";
  
  // Remember custom voice text from previous sessions
  if(req.cookies.aiVoice) {
    txt = decodeURIComponent(req.cookies.aiVoice);
  }
  
  const request = {
    input: {text: txt},
    // Use remembered language preference or default
    voice: {languageCode: (req.cookies.langCode || 'en-US'), ssmlGender: 'MALE'},
    audioConfig: {audioEncoding: 'MP3'},
  };
});
```

**Dynamic Content Translation:**

```javascript
// Translation block with preference tracking
app.post('/translate-block', async function(req, res) {
  let langCode = 'en-US';
  
  // Check user's saved language preference
  if(req.cookies && req.cookies['langCode']) {
    langCode = req.cookies['langCode'];
  }
  
  // Allow override for current request
  if(req.body.langCode) {
    langCode = req.body.langCode;
  }
  
  let [translations] = await translate.translate(req.body.block, langCode);
  res.json({results: translations});
});
```

**HTML Language Attribute Personalization:**

```javascript
// Template rendering with language preference
let langOverride = '';
if(req.cookies && req.cookies['langCode']) {
  langOverride = ` lang="${req.cookies['langCode'].split('-')[0]}"`;
}

let headSection = `<!doctype html>
<html${langOverride}>
  <head>
    <!-- Localized content -->
  </head>
</html>`;
```

### 3. **Conversation Context Memory**

**Input Language Tracking:**

```javascript
app.post('/ai', async function(req, res) {
  let convo = req.body.chat;
  // Remember user's input language preference
  let inputLanguage = req.body.inputLanguage || null;
  
  // Context checking to avoid duplicate system prompts
  if(JSON.stringify(convo).indexOf('Narrowing Down') == -1) {
    // Add system prompt only if not already in conversation flow
  }
});
```

**Conversation State Preservation:**
The system maintains conversation continuity by checking for specific phrases in the conversation history, preventing duplicate system prompts and maintaining context across multiple exchanges.

### 4. **Search Personalization**

**Visitor ID Tracking for Personalized Search:**

```javascript
// Autocomplete with visitor tracking
app.post('/autocomplete*', async (req, res) => {
  // Attach visitor ID for personalized suggestions
  if(req.cookies && req.cookies['gbi_visitorId']) {
    req.body.visitorId = req.cookies['gbi_visitorId'];
  }
  
  // Handle multilingual autocomplete
  if(req.cookies && req.cookies['langCode'] && req.cookies['txUI']) {
    // Translate query to English for search, then provide localized results
    let [translations] = await translate.translate(decodeURIComponent(req.query.q), 'en-US');
    let auto = await axios.get(`https://autocomplete.demos.groupbycloud.com/api/request?query=${translations[0]}`);
  }
});
```

**Search API with Visitor Context:**

```javascript
app.post('/search-api', async function(req, res) {
  // Include visitor ID for personalized search results
  if(req.cookies && req.cookies['gbi_visitorId']) {
    req.body.visitorId = req.cookies['gbi_visitorId'];
  }
  
  let search = await axios.post('https://search.demos.groupbycloud.com/api/search', req.body, options);
});
```

### 5. **UI Personalization**

**Dynamic Feature Loading:**

```javascript
// AI-enhanced UI features based on user preference
if(req.cookies && req.cookies['aiSearch']) {
  jsHeaderList += `<link type="text/css" href="/assets/ai-pdp.css?v=${dTime}" rel="stylesheet" />
  <script src="/assets/ai-pdp.js?v=${dTime}"></script>`;
}
```

**Megamenu Customization:**

```javascript
// Personalized navigation menu
let defaultMegamenu = 'housewares-megamenu';
if(req.cookies && req.cookies['megamenuOverride']) {
  defaultMegamenu = req.cookies['megamenuOverride'];
}

let megaData = await axios.get(`https://cm.demos.groupbycloud.com/api/megamenus/${defaultMegamenu}/categories`);
```

### 6. **Session Navigation Memory**

**Page State Preservation:**

```javascript
// Remember where user was before authentication
if(req.cookies.currentPg && req.cookies.currentPg != '') {
  let gotoPg = req.cookies.currentPg;
  res.cookie('currentPg','', { maxAge: 1, httpOnly: true }); // Clear after use
  res.redirect(gotoPg);
}

// Save current page for post-auth redirect
res.cookie('currentPg', decodeURIComponent(req.url), { maxAge: 1000*60*5, httpOnly: true });
```

### 7. **Cross-Request Context Sharing**

**GroupBy Visitor Tracking:**

```javascript
// Frontend visitor tracking initialization
<script>
var currentPg = "${currentPg}";
const gbTracker = new GbTracker('demos', 'Production');
gbTracker.autoSetVisitor(); // Establishes persistent visitor identity
</script>
```

### Key Personalization Features:

1. **Language Persistence**: User's language choice persists across sessions for UI, voice, and search
2. **Conversation Memory**: System remembers conversation state to avoid repetitive prompts
3. **Search History**: Visitor ID enables personalized search recommendations
4. **UI Adaptation**: Interface features adapt based on user preferences (AI search, menu layout)
5. **Navigation Memory**: System remembers user's intended destination across login flows
6. **Voice Preferences**: Custom voice messages and language settings persist
7. **Translation Context**: Bilingual users get seamless translation support

This multi-layered personalization creates a shopping experience that adapts to individual user preferences, language needs, and behavioral patterns, making each conversation feel tailored and contextually relevant.

## Key Design Patterns & Architectural Insights

### 1. **Conversational State Machine**
The system implements a sophisticated state machine that categorizes user intent into discrete response codes (0, 1, 2), enabling predictable conversation flow management.

### 2. **Multi-Modal Integration**
Seamless integration of text, voice, and image inputs creates a rich conversational experience that mimics human sales interactions.

### 3. **Context-Aware Personalization**
The system remembers user preferences (language, previous searches) and adapts responses accordingly, creating personalized shopping experiences.

### 4. **Structured Response Format**
The colon-delimited response format enables frontend parsing while maintaining AI flexibility, demonstrating effective AI-frontend integration patterns.

### 5. **Microservice-Ready Architecture**
Despite being a monolithic demo, the code structure clearly separates concerns (authentication, translation, search, conversation) in a way that could easily be decomposed into microservices.

## Implementation Recommendations for POC

### Immediate Value Extractions
1. **Response Code System**: Consider the same 0/1/2 classification system for conversation flow management (TBD)
2. **Search Term Enhancement**: Use AI to automatically improve search queries with contextual terms
3. **Multi-Modal Input**: Integrate voice and image inputs for richer user interactions
4. **Structured Output**: Leverage OpenAI's JSON Schema for predictable response formats

### Architecture Adaptations
1. **Java Backend Migration**: Convert Node.js logic to Java-based orchestrator service
2. **Microservice Decomposition**: Separate conversation, search, translation, and user management
3. **State Management**: Implement proper session management for conversation continuity
4. **Request Handling**: Add proper request routing and rate limiting (the latter is optional for POC)

### Critical Success Factors
1. **Conversation Quality**: Focus on natural dialogue flow over feature breadth
2. **Response Speed**: Optimize AI response times for real-time conversation feel
3. **Context Preservation**: Maintain conversation context across multiple exchanges
4. **Graceful Error Handling**: Ensure system degradation doesn't break conversation flow

This prototype demonstrates that sophisticated conversational commerce is achievable with current AI technology, providing a solid foundation for building a production-ready Conversational Commerce Engine that can transform traditional e-commerce into engaging, personalized shopping experiences.
